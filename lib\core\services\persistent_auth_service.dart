import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:logger/logger.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'dart:convert';

final persistentAuthServiceProvider = StateProvider<PersistentAuthService>((ref) => PersistentAuthService());

class PersistentAuthService {
  final _logger = Logger();
  final _supabaseClient = Supabase.instance.client;
  
  // مفاتيح التخزين المحلي
  static const String _userDataKey = 'user_data';
  static const String _isLoggedInKey = 'is_logged_in';
  static const String _lastLoginKey = 'last_login';
  static const String _offlineModeKey = 'offline_mode';
  
  /// فحص حالة تسجيل الدخول
  Future<bool> isUserLoggedIn() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isLoggedIn = prefs.getBool(_isLoggedInKey) ?? false;
      
      if (!isLoggedIn) {
        return false;
      }
      
      // فحص الاتصال بالإنترنت
      final connectivityResult = await Connectivity().checkConnectivity();
      final hasInternet = connectivityResult != ConnectivityResult.none;
      
      if (hasInternet) {
        // إذا كان هناك إنترنت، تحقق من صحة الجلسة
        final currentUser = _supabaseClient.auth.currentUser;
        if (currentUser != null) {
          _logger.i('User is logged in online: ${currentUser.id}');
          return true;
        } else {
          // محاولة استعادة الجلسة
          return await _restoreSession();
        }
      } else {
        // في وضع عدم الاتصال، اعتمد على البيانات المحلية
        _logger.i('User is in offline mode');
        await _setOfflineMode(true);
        return true;
      }
    } catch (e) {
      _logger.e('Error checking login status: $e');
      return false;
    }
  }
  
  /// حفظ بيانات المستخدم محلياً
  Future<void> saveUserData(Map<String, dynamic> userData) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_userDataKey, jsonEncode(userData));
      await prefs.setBool(_isLoggedInKey, true);
      await prefs.setString(_lastLoginKey, DateTime.now().toIso8601String());
      
      _logger.i('User data saved locally');
    } catch (e) {
      _logger.e('Error saving user data: $e');
    }
  }
  
  /// استرجاع بيانات المستخدم المحلية
  Future<Map<String, dynamic>?> getUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userDataString = prefs.getString(_userDataKey);
      
      if (userDataString != null) {
        return jsonDecode(userDataString) as Map<String, dynamic>;
      }
      
      return null;
    } catch (e) {
      _logger.e('Error getting user data: $e');
      return null;
    }
  }
  
  /// تسجيل الخروج
  Future<void> logout() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_userDataKey);
      await prefs.setBool(_isLoggedInKey, false);
      await prefs.remove(_lastLoginKey);
      await _setOfflineMode(false);
      
      // تسجيل الخروج من Supabase إذا كان متصلاً
      final connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult != ConnectivityResult.none) {
        await _supabaseClient.auth.signOut();
      }
      
      _logger.i('User logged out successfully');
    } catch (e) {
      _logger.e('Error during logout: $e');
    }
  }
  
  /// استعادة الجلسة
  Future<bool> _restoreSession() async {
    try {
      final userData = await getUserData();
      if (userData == null) {
        return false;
      }
      
      // محاولة تجديد الجلسة
      await _supabaseClient.auth.refreshSession();
      final currentUser = _supabaseClient.auth.currentUser;
      
      if (currentUser != null) {
        _logger.i('Session restored successfully');
        return true;
      } else {
        _logger.w('Failed to restore session');
        return false;
      }
    } catch (e) {
      _logger.e('Error restoring session: $e');
      return false;
    }
  }
  
  /// تعيين وضع عدم الاتصال
  Future<void> _setOfflineMode(bool isOffline) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_offlineModeKey, isOffline);
    } catch (e) {
      _logger.e('Error setting offline mode: $e');
    }
  }
  
  /// فحص وضع عدم الاتصال
  Future<bool> isOfflineMode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_offlineModeKey) ?? false;
    } catch (e) {
      _logger.e('Error checking offline mode: $e');
      return false;
    }
  }
  
  /// فحص الاتصال بالإنترنت
  Future<bool> hasInternetConnection() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      return connectivityResult != ConnectivityResult.none;
    } catch (e) {
      _logger.e('Error checking internet connection: $e');
      return false;
    }
  }
  
  /// مزامنة البيانات عند الاتصال بالإنترنت
  Future<void> syncWhenOnline() async {
    try {
      final hasInternet = await hasInternetConnection();
      if (hasInternet && await isOfflineMode()) {
        _logger.i('Back online - starting sync...');
        await _setOfflineMode(false);
        
        // هنا يمكن إضافة منطق مزامنة البيانات المحلية
        // مثل رفع الصور والفيديوهات المحفوظة محلياً
      }
    } catch (e) {
      _logger.e('Error during sync: $e');
    }
  }
}
