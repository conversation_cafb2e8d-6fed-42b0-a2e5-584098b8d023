import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class VideoWatermarkOverlay extends StatelessWidget {
  final String username;
  final String location;
  final Size videoSize;

  const VideoWatermarkOverlay({
    super.key,
    required this.username,
    required this.location,
    required this.videoSize,
  });

  String _formatDate(DateTime date) {
    final List<String> arabicMonths = [
      'يناير', 'فبراير', 'مارس', 'إبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];  
    return '${date.day} ${arabicMonths[date.month - 1]} ${date.year}';
  }

  String _formatTime(DateTime time) {
    final hour = time.hour % 12 == 0 ? 12 : time.hour % 12;
    final minute = time.minute.toString().padLeft(2, '0');
    final period = time.hour < 12 ? 'ص' : 'م';
    final dayNightIndicator = time.hour >= 6 && time.hour < 18 ? '☀️' : '🌙';
    return '$hour:$minute $period $dayNightIndicator';
  }

  @override
  Widget build(BuildContext context) {
    final now = DateTime.now();
    final dateStr = _formatDate(now);
    final timeStr = _formatTime(now);

    // حساب الأحجام المتجاوبة مع حماية من القيم الصفرية
    final screenWidth = MediaQuery.of(context).size.width;
    final baseWidth = videoSize.width > 0 ? videoSize.width : screenWidth;

    // أحجام صغيرة جداً جداً
    final double iconSize = baseWidth * 0.03;  // أصغر جداً جداً
    final double locationFontSize = baseWidth * 0.008;  // صغير جداً
    final double dateFontSize = baseWidth * 0.009;  // صغير جداً
    final double timeFontSize = baseWidth * 0.01;  // صغير جداً
    final double usernameFontSize = baseWidth * 0.008;  // صغير جداً





    return Stack(
      children: [
        // الشعار الأيمن
        Positioned(
          top: 4,
          right: 4,
          child: Image.asset(
            'assets/images/icon_right.png',
            width: iconSize,
            height: iconSize,
          ),
        ),

        // الشعار الأيسر
        Positioned(
          top: 4,
          left: 4,
          child: Image.asset(
            'assets/images/icon_left.png',
            width: iconSize,
            height: iconSize,
          ),
        ),

        // معلومات الموقع والتاريخ والوقت (أقصى أسفل اليمين)
        Positioned(
          bottom: 2,
          right: 2,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisSize: MainAxisSize.min,
            children: [
              // الموقع
              Text(
                location,
                style: GoogleFonts.cairo(
                  color: Colors.white,
                  fontSize: locationFontSize,
                  fontWeight: FontWeight.bold,
                  shadows: const [
                    Shadow(
                      color: Colors.black,
                      blurRadius: 1,
                      offset: Offset(0.5, 0.5),
                    ),
                  ],
                ),
                textDirection: TextDirection.rtl,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),

              // التاريخ
              Text(
                dateStr,
                style: GoogleFonts.cairo(
                  color: Colors.white,
                  fontSize: dateFontSize,
                  fontWeight: FontWeight.bold,
                  shadows: const [
                    Shadow(
                      color: Colors.black,
                      blurRadius: 1,
                      offset: Offset(0.5, 0.5),
                    ),
                  ],
                ),
                textDirection: TextDirection.rtl,
              ),

              // الوقت
              Text(
                timeStr,
                style: GoogleFonts.cairo(
                  color: Colors.white,
                  fontSize: timeFontSize,
                  fontWeight: FontWeight.bold,
                  shadows: const [
                    Shadow(
                      color: Colors.black,
                      blurRadius: 1,
                      offset: Offset(0.5, 0.5),
                    ),
                  ],
                ),
                textDirection: TextDirection.rtl,
              ),
            ],
          ),
        ),

        // اسم المستخدم (أقصى أسفل اليسار)
        Positioned(
          bottom: 2,
          left: 2,
          child: Text(
            username,
            style: GoogleFonts.cairo(
              color: Colors.white,
              fontSize: usernameFontSize,
              fontWeight: FontWeight.bold,
              shadows: const [
                Shadow(
                  color: Colors.black,
                  blurRadius: 1,
                  offset: Offset(0.5, 0.5),
                ),
              ],
            ),
            textDirection: TextDirection.rtl,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}
