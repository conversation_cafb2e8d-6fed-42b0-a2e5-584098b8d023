import 'dart:io';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart' as supabase;
import 'package:easy_localization/easy_localization.dart';
import 'package:logger/logger.dart';
import 'app_exceptions.dart';

/// معالج الأخطاء العام
class ErrorHandler {
  static final _logger = Logger();

  /// معالجة الأخطاء وتحويلها إلى استثناءات مخصصة
  static AppException handleError(dynamic error) {
    _logger.e('Error occurred: $error');

    if (error is AppException) {
      return error;
    }

    // معالجة أخطاء Supabase
    if (error is supabase.AuthException) {
      return _handleAuthError(error);
    }

    // معالجة أخطاء الشبكة
    if (error is SocketException) {
      return const NetworkException(
        'No internet connection',
        code: 'no_internet',
      );
    }

    if (error is HttpException) {
      return NetworkException(
        'HTTP Error: ${error.message}',
        code: 'http_error',
        originalError: error,
      );
    }

    // معالجة أخطاء عامة
    return GeneralException(
      error.toString(),
      originalError: error,
    );
  }

  /// معالجة أخطاء المصادقة
  static AuthException _handleAuthError(supabase.AuthException error) {
    switch (error.message.toLowerCase()) {
      case 'invalid login credentials':
      case 'invalid email or password':
        return const AuthException(
          'Invalid credentials',
          code: 'invalid_credentials',
        );
      case 'email not confirmed':
        return const AuthException(
          'Email not confirmed',
          code: 'email_not_confirmed',
        );
      case 'user not found':
        return const AuthException(
          'User not found',
          code: 'user_not_found',
        );
      default:
        return AuthException(
          error.message,
          code: 'general_error',
          originalError: error,
        );
    }
  }

  /// عرض رسالة خطأ للمستخدم
  static void showErrorSnackBar(BuildContext context, AppException error) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(error.getLocalizedMessage()),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        action: SnackBarAction(
          label: 'common.ok'.tr(),
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  /// عرض حوار خطأ
  static Future<void> showErrorDialog(
    BuildContext context,
    AppException error, {
    VoidCallback? onRetry,
  }) async {
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('common.error'.tr()),
        content: Text(error.getLocalizedMessage()),
        actions: [
          if (onRetry != null)
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                onRetry();
              },
              child: Text('common.retry'.tr()),
            ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('common.ok'.tr()),
          ),
        ],
      ),
    );
  }

  /// تسجيل الخطأ للمراقبة
  static void logError(AppException error, {StackTrace? stackTrace}) {
    _logger.e(
      'App Error: ${error.message}',
      error: error.originalError,
      stackTrace: stackTrace,
    );

    // هنا يمكن إضافة تكامل مع خدمات المراقبة مثل Crashlytics
    // FirebaseCrashlytics.instance.recordError(error, stackTrace);
  }

  /// معالجة الأخطاء غير المتوقعة
  static void handleUnexpectedError(dynamic error, StackTrace stackTrace) {
    final appError = handleError(error);
    logError(appError, stackTrace: stackTrace);
  }
}
