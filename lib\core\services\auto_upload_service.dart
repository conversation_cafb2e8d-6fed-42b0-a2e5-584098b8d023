import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../utils/logger.dart';

/// خدمة الرفع التلقائي للملفات
class AutoUploadService {
  static final AutoUploadService _instance = AutoUploadService._internal();
  factory AutoUploadService() => _instance;
  AutoUploadService._internal();

  final _logger = getLogger();
  Timer? _uploadTimer;
  bool _isUploading = false;

  // الحصول على عميل Supabase
  SupabaseClient get _supabase => Supabase.instance.client;

  /// بدء خدمة الرفع التلقائي
  void startAutoUpload() {
    _logger.i('Starting auto upload service');
    
    // إيقاف المؤقت السابق إن وجد
    _uploadTimer?.cancel();
    
    // بدء مؤقت للفحص كل 30 ثانية
    _uploadTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _checkAndUploadPendingFiles();
    });
    
    // فحص فوري عند البدء
    _checkAndUploadPendingFiles();
  }

  /// إيقاف خدمة الرفع التلقائي
  void stopAutoUpload() {
    _logger.i('Stopping auto upload service');
    _uploadTimer?.cancel();
    _uploadTimer = null;
  }

  /// فحص ورفع الملفات المعلقة
  Future<void> _checkAndUploadPendingFiles() async {
    if (_isUploading) {
      _logger.d('Upload already in progress, skipping');
      return;
    }

    try {
      _isUploading = true;
      _logger.d('Starting upload check...');
      
      // فحص الاتصال بالإنترنت
      final connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult.contains(ConnectivityResult.none)) {
        _logger.d('No internet connection, skipping upload');
        return;
      }

      // الحصول على الملفات المعلقة
      final prefs = await SharedPreferences.getInstance();
      final pendingUploads = prefs.getStringList('pending_uploads') ?? [];
      
      if (pendingUploads.isEmpty) {
        _logger.d('No pending uploads found');
        return;
      }

      _logger.i('Found ${pendingUploads.length} pending uploads');

      final List<String> successfulUploads = [];
      final List<String> failedUploads = [];

      for (final uploadJson in pendingUploads) {
        try {
          final uploadData = jsonDecode(uploadJson) as Map<String, dynamic>;
          
          // تخطي الملفات التي تم رفعها بالفعل
          if (uploadData['status'] == 'uploaded') {
            successfulUploads.add(uploadJson);
            continue;
          }

          // محاولة رفع الملف مع timeout
          final success = await _uploadFile(uploadData).timeout(
            const Duration(minutes: 2),
            onTimeout: () {
              _logger.w('Upload timeout for: ${uploadData['file_name']}');
              return false;
            },
          );
          
          if (success) {
            uploadData['status'] = 'uploaded';
            uploadData['uploaded_at'] = DateTime.now().toIso8601String();
            successfulUploads.add(jsonEncode(uploadData));
            _logger.i('Successfully uploaded: ${uploadData['file_name']}');

            // مسح الملف من التخزين المحلي بعد الرفع بنجاح (إذا كان مفعلاً)
            if (_autoDeleteEnabled) {
              await _deleteLocalFile(uploadData['file_path'] as String);
            }
          } else {
            uploadData['status'] = 'failed';
            uploadData['last_attempt'] = DateTime.now().toIso8601String();
            failedUploads.add(jsonEncode(uploadData));
            _logger.w('Failed to upload: ${uploadData['file_name']}');
          }
        } catch (e) {
          _logger.e('Error processing upload: $e');
          failedUploads.add(uploadJson);
        }
      }

      // تحديث قائمة الملفات المعلقة (الاحتفاظ بالفاشلة فقط)
      await prefs.setStringList('pending_uploads', failedUploads);
      
      // حفظ الملفات المرفوعة بنجاح في قائمة منفصلة
      if (successfulUploads.isNotEmpty) {
        final uploadedFiles = prefs.getStringList('uploaded_files') ?? [];
        uploadedFiles.addAll(successfulUploads);
        await prefs.setStringList('uploaded_files', uploadedFiles);
      }

      _logger.i('Upload batch completed: ${successfulUploads.length} successful, ${failedUploads.length} failed');

    } catch (e) {
      _logger.e('Error in auto upload service: $e');
    } finally {
      _isUploading = false;
      _logger.d('Upload process finished, flag reset');
    }
  }

  /// رفع ملف واحد
  Future<bool> _uploadFile(Map<String, dynamic> uploadData) async {
    try {
      final filePath = uploadData['file_path'] as String;
      final fileName = uploadData['file_name'] as String;
      final location = uploadData['location'] as String;
      final fileType = uploadData['type'] as String; // 'photo' or 'video'

      // التحقق من وجود الملف
      final file = File(filePath);
      if (!await file.exists()) {
        _logger.w('File not found: $filePath');
        return false;
      }

      // رفع الملف حسب النوع
      if (fileType == 'photo') {
        return await _uploadPhoto(file, fileName, location);
      } else if (fileType == 'video') {
        return await _uploadVideo(file, fileName, location);
      } else {
        _logger.w('Unknown file type: $fileType');
        return false;
      }
    } catch (e) {
      _logger.e('Error uploading file: $e');
      return false;
    }
  }

  /// رفع صورة
  Future<bool> _uploadPhoto(File file, String fileName, String location) async {
    try {
      // رفع الصورة إلى Supabase Storage
      final response = await _supabase.storage
          .from('photos')
          .upload('uploads/$fileName', file);

      if (response.isNotEmpty) {
        // الحصول على رابط الصورة
        final imageUrl = _supabase.storage
            .from('photos')
            .getPublicUrl('uploads/$fileName');

        // حفظ معلومات الصورة في قاعدة البيانات
        await _supabase.from('photos').insert({
          'file_name': fileName,
          'url': imageUrl,  // تغيير من file_url إلى url
          'location': location,
          'created_at': DateTime.now().toIso8601String(),  // تغيير من uploaded_at إلى created_at
          'user_id': _supabase.auth.currentUser?.id,
        });

        return true;
      }
      return false;
    } catch (e) {
      _logger.e('Error uploading photo: $e');
      return false;
    }
  }

  /// رفع فيديو
  Future<bool> _uploadVideo(File file, String fileName, String location) async {
    try {
      // رفع الفيديو إلى Supabase Storage
      final response = await _supabase.storage
          .from('videos')
          .upload('uploads/$fileName', file);

      if (response.isNotEmpty) {
        // الحصول على رابط الفيديو
        final videoUrl = _supabase.storage
            .from('videos')
            .getPublicUrl('uploads/$fileName');

        // حفظ معلومات الفيديو في قاعدة البيانات
        await _supabase.from('videos').insert({
          'file_name': fileName,
          'url': videoUrl,  // تغيير من file_url إلى url
          'location': location,
          'created_at': DateTime.now().toIso8601String(),  // تغيير من uploaded_at إلى created_at
          'user_id': _supabase.auth.currentUser?.id,
        });

        return true;
      }
      return false;
    } catch (e) {
      _logger.e('Error uploading video: $e');
      return false;
    }
  }

  /// الحصول على إحصائيات الرفع
  Future<Map<String, int>> getUploadStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final pendingUploads = prefs.getStringList('pending_uploads') ?? [];
      final uploadedFiles = prefs.getStringList('uploaded_files') ?? [];
      
      return {
        'pending': pendingUploads.length,
        'uploaded': uploadedFiles.length,
        'total': pendingUploads.length + uploadedFiles.length,
      };
    } catch (e) {
      _logger.e('Error getting upload stats: $e');
      return {'pending': 0, 'uploaded': 0, 'total': 0};
    }
  }

  /// مسح الملفات المرفوعة من التخزين المحلي
  Future<void> clearUploadedFiles() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('uploaded_files');
      _logger.i('Cleared uploaded files list');
    } catch (e) {
      _logger.e('Error clearing uploaded files: $e');
    }
  }

  /// إعادة محاولة رفع الملفات الفاشلة
  Future<void> retryFailedUploads() async {
    _logger.i('Retrying failed uploads');
    await _checkAndUploadPendingFiles();
  }

  /// مسح ملف من التخزين المحلي
  Future<void> _deleteLocalFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        _logger.i('Deleted local file: $filePath');
      } else {
        _logger.w('File not found for deletion: $filePath');
      }
    } catch (e) {
      _logger.e('Error deleting local file: $e');
    }
  }

  /// مسح جميع الملفات المرفوعة من التخزين المحلي
  Future<void> deleteAllUploadedFiles() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final uploadedFiles = prefs.getStringList('uploaded_files') ?? [];

      int deletedCount = 0;
      for (final uploadJson in uploadedFiles) {
        try {
          final uploadData = jsonDecode(uploadJson) as Map<String, dynamic>;
          final filePath = uploadData['file_path'] as String;
          await _deleteLocalFile(filePath);
          deletedCount++;
        } catch (e) {
          _logger.e('Error deleting uploaded file: $e');
        }
      }

      _logger.i('Deleted $deletedCount uploaded files from local storage');
    } catch (e) {
      _logger.e('Error deleting uploaded files: $e');
    }
  }

  /// تفعيل/إلغاء تفعيل المسح التلقائي
  static bool _autoDeleteEnabled = true;

  static bool get autoDeleteEnabled => _autoDeleteEnabled;

  static void setAutoDelete(bool enabled) {
    _autoDeleteEnabled = enabled;
  }
}
