import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:video_player/video_player.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:io';
import 'dart:convert';
import 'package:path_provider/path_provider.dart';
import '../../../core/utils/logger.dart';
import '../../../core/utils/video_processor.dart';
import '../../../core/services/supabase_service.dart';
import '../../../core/services/auto_upload_service.dart';
import './widgets/video_watermark_overlay.dart';

class VideoPreviewScreen extends ConsumerStatefulWidget {
  final String videoPath;
  final String? selectedLocation;
  final String? currentAddress; // الموقع الجغرافي الحقيقي

  const VideoPreviewScreen({
    super.key,
    required this.videoPath,
    this.selectedLocation,
    this.currentAddress,
  });

  @override
  ConsumerState<VideoPreviewScreen> createState() => _VideoPreviewScreenState();
}

class _VideoPreviewScreenState extends ConsumerState<VideoPreviewScreen> {
  final _logger = getLogger();
  VideoPlayerController? _controller;
  bool _isLoading = true;
  bool _isUploading = false;
  String? _processedVideoPath;
  String _username = '';
  String _location = '';

  @override
  void initState() {
    super.initState();
    _initializeVideo();
    _processVideo(); // معالجة الفيديو (تتضمن تحميل معلومات المستخدم)
  }

  Future<void> _initializeVideo() async {
    try {
      _controller = VideoPlayerController.file(File(widget.videoPath));
      await _controller!.initialize();
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      _logger.e('Error initializing video: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  Future<void> _loadUserInfo() async {
    try {
      // الحصول على معلومات المستخدم
      final user = ref.read(supabaseServiceProvider).client.auth.currentUser;

      if (user != null) {
        final response = await ref.read(supabaseServiceProvider).client
            .from('users')
            .select('full_name')
            .eq('id', user.id)
            .single();

        if (mounted) {
          setState(() {
            _username = response['full_name'] as String? ?? 'مستخدم';
            _location = widget.currentAddress ?? 'لم يتم تحديد الموقع';
          });
        }
      } else {
        // إذا لم يكن هناك مستخدم مسجل، استخدم قيم افتراضية
        if (mounted) {
          setState(() {
            _username = 'مستخدم';
            _location = widget.currentAddress ?? 'لم يتم تحديد الموقع';
          });
        }
      }
    } catch (e) {
      _logger.e('Error loading user info: $e');
      if (mounted) {
        setState(() {
          _username = 'مستخدم';
          _location = widget.currentAddress ?? 'لم يتم تحديد الموقع';
        });
      }
    }
  }



  Future<void> _processVideo() async {
    try {
      // انتظار تحميل معلومات المستخدم أولاً
      await _loadUserInfo();

      final processedPath = await VideoProcessor.processVideo(
        videoPath: widget.videoPath,
        username: _username.isNotEmpty ? _username : 'مستخدم',
        location: _location.isNotEmpty ? _location : 'الموقع',
      );

      if (mounted) {
        setState(() {
          _processedVideoPath = processedPath;
        });
      }
    } catch (e) {
      _logger.e('Error processing video: $e');
    }
  }

  Future<void> _saveVideo() async {
    setState(() {
      _isUploading = true;
    });

    try {
      // حفظ الفيديو في المعرض المحلي دائماً
      await _saveToGallery();

      // بدء الرفع التلقائي في الخلفية
      _startBackgroundUpload();

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'video.saved_to_gallery'.tr(),
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: Colors.green,
        ),
      );

      setState(() {
        _isUploading = false;
      });

    } catch (e) {
      _logger.e('Error saving video: $e');

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'errors.general'.tr(),
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isUploading = false;
        });
      }
    }
  }

  Future<void> _saveToGallery() async {
    // إنشاء مجلد المعرض المحلي
    final directory = await getApplicationDocumentsDirectory();
    final galleryDir = Directory('${directory.path}/gallery');
    if (!await galleryDir.exists()) {
      await galleryDir.create(recursive: true);
    }

    // نسخ الفيديو المعالج إلى المعرض
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final fileName = 'video_$timestamp.mp4';
    final savedPath = '${galleryDir.path}/$fileName';

    // استخدام الفيديو المعالج إذا كان متاحاً، وإلا استخدم الأصلي
    final sourceVideoPath = _processedVideoPath ?? widget.videoPath;
    await File(sourceVideoPath).copy(savedPath);

    // حفظ معلومات الفيديو للرفع التلقائي
    await _saveUploadMetadata(savedPath, fileName);

    // حذف الملف المؤقت
    await File(widget.videoPath).delete();

    _logger.i('Video saved to gallery: $savedPath');
  }

  Future<void> _saveUploadMetadata(String filePath, String fileName) async {
    final prefs = await SharedPreferences.getInstance();
    final pendingUploads = prefs.getStringList('pending_uploads') ?? [];

    final metadata = {
      'file_path': filePath,
      'file_name': fileName,
      'location': widget.selectedLocation ?? 'unknown',
      'created_at': DateTime.now().toIso8601String(),
      'type': 'video',
      'status': 'pending', // pending, uploading, uploaded, failed
    };

    pendingUploads.add(jsonEncode(metadata));
    await prefs.setStringList('pending_uploads', pendingUploads);
  }

  void _startBackgroundUpload() {
    // بدء الرفع التلقائي في الخلفية
    AutoUploadService().startAutoUpload();
    _logger.i('Background upload started for video');
  }

  Future<void> _retakeVideo() async {
    try {
      // حذف الفيديو المؤقت
      await File(widget.videoPath).delete();

      // العودة للكاميرا
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      _logger.e('Error deleting video: $e');
      if (mounted) {
        Navigator.of(context).pop();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // عرض الفيديو
          if (_isLoading)
            const Center(
              child: CircularProgressIndicator(
                color: Color(0xFFD4AF37),
              ),
            )
          else if (_controller != null && _controller!.value.isInitialized)
            Center(
              child: AspectRatio(
                aspectRatio: _controller!.value.aspectRatio,
                child: Stack(
                  children: [
                    VideoPlayer(_controller!),
                    // العلامات المائية - تظهر دائماً
                    VideoWatermarkOverlay(
                      username: _username.isNotEmpty ? _username : 'مستخدم',
                      location: _location.isNotEmpty ? _location : 'الموقع',
                      videoSize: _controller!.value.size,
                    ),
                    // مؤشر تحميل معلومات المستخدم
                    if (_username.isEmpty)
                      Positioned(
                        top: 100,
                        right: 20,
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 0.7),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              SizedBox(
                                width: 12,
                                height: 12,
                                child: CircularProgressIndicator(
                                  strokeWidth: 1.5,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              ),
                              SizedBox(width: 6),
                              Text(
                                'تحميل...',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            )
          else
            const Center(
              child: Icon(
                Icons.error,
                color: Colors.red,
                size: 64,
              ),
            ),

          // أزرار التحكم
          if (!_isLoading && _controller != null && _controller!.value.isInitialized)
            Positioned(
              bottom: 100,
              left: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 40),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // زر إعادة التصوير
                    _buildActionButton(
                      icon: Icons.refresh,
                      label: 'common.retake'.tr(),
                      color: Colors.white,
                      onTap: _retakeVideo,
                    ),
                    
                    // زر تشغيل/إيقاف
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          if (_controller!.value.isPlaying) {
                            _controller!.pause();
                          } else {
                            _controller!.play();
                          }
                        });
                      },
                      child: Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: Colors.white,
                            width: 2,
                          ),
                        ),
                        child: Icon(
                          _controller!.value.isPlaying ? Icons.pause : Icons.play_arrow,
                          color: Colors.white,
                          size: 40,
                        ),
                      ),
                    ),
                    
                    // زر الحفظ
                    _buildActionButton(
                      icon: _isUploading ? Icons.hourglass_empty : Icons.check,
                      label: _isUploading ? 'common.uploading'.tr() : 'common.save'.tr(),
                      color: const Color(0xFFD4AF37),
                      onTap: _isUploading ? null : _saveVideo,
                    ),
                  ],
                ),
              ),
            ),



          // زر الإغلاق
          Positioned(
            top: 50,
            left: 20,
            child: IconButton(
              onPressed: _retakeVideo,
              icon: const Icon(
                Icons.close,
                color: Colors.white,
                size: 30,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.2),
              shape: BoxShape.circle,
              border: Border.all(
                color: color,
                width: 2,
              ),
            ),
            child: Icon(
              icon,
              color: color,
              size: 30,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: GoogleFonts.cairo(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
