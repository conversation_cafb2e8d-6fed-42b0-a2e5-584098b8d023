name: moon_memory_camera
description: "تطبيق كاميرا ذاكرة القمر لتوثيق الإنجازات"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
dependencies:
  # Flutter SDK dependencies
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  
  # State Management
  flutter_riverpod: ^2.4.9
  
  # Database & Backend
  supabase_flutter: ^2.0.2
  
  # Localization
  easy_localization: ^3.0.3
  async: ^2.12.0
  intl: ^0.19.0
  
  # Device & System
  device_info_plus: ^11.5.0
  permission_handler: ^12.0.1
  connectivity_plus: ^6.0.5
  
  # Storage & Caching
  shared_preferences: ^2.2.2
  cached_network_image: ^3.3.1
  cached_network_image_platform_interface: ^4.0.0
  cached_network_image_web: ^1.2.0
  
  # Media & Camera
  camera: ^0.11.2
  image_picker: ^1.0.4
  image: ^4.1.3
  video_player: ^2.8.2
  
  # Location
  geolocator: ^14.0.2
  geocoding: ^4.0.0
  
  # Utilities
  path_provider: ^2.1.2
  path: ^1.8.3
  crypto: ^3.0.3
  uuid: ^4.5.1
  logger: ^2.5.0
  flutter_dotenv: ^5.1.0
  
  # UI
  google_fonts: ^6.2.1
  share_plus: ^7.2.1

environment:
  sdk: ^3.6.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_launcher_icons: ^0.13.1
  flutter_lints: ^5.0.0
  mockito: ^5.4.4
  build_runner: ^2.4.7
  path_provider_platform_interface: ^2.1.1
  plugin_platform_interface: ^2.1.7

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icons/app_icon.png"
  min_sdk_android: 21
  remove_alpha_ios: true
  adaptive_icon_background: "#FFFFFF"
  adaptive_icon_foreground: "assets/icons/app_icon.png"

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/translations/
    - assets/images/
    - assets/icons/
    - .env

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
