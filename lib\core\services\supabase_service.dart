import 'package:flutter_riverpod/flutter_riverpod.dart' hide Provider;
import 'package:supabase_flutter/supabase_flutter.dart' as supabase;

class SupabaseService {
  final supabase.SupabaseClient client;

  SupabaseService(this.client);

  static SupabaseService initialize() {
    return SupabaseService(supabase.Supabase.instance.client);
  }
}

final supabaseServiceProvider = StateProvider<SupabaseService>((ref) {
  return SupabaseService.initialize();
});
