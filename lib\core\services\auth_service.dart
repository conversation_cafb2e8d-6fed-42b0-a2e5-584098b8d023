import 'package:flutter_riverpod/flutter_riverpod.dart' hide Provider;
import 'package:supabase_flutter/supabase_flutter.dart' as supabase;
import 'package:logger/logger.dart';
import '../errors/app_exceptions.dart';
import '../errors/error_handler.dart';
import 'device_service.dart';
import 'enhanced_device_auth_service.dart';
import 'persistent_auth_service.dart';

final authServiceProvider = StateProvider<AuthService>((ref) => AuthService());

class AuthService {
  final _supabaseClient = supabase.Supabase.instance.client;
  final _logger = Logger();
  final _deviceService = DeviceService();
  final _enhancedDeviceAuth = EnhancedDeviceAuthService();

  /// Get device information
  Future<Map<String, dynamic>> _getDeviceInfo() async {
    try {
      final deviceInfo = await _deviceService.getDeviceInfo();
      _logger.i('Device info: $deviceInfo');
      return deviceInfo;
    } catch (e) {
      _logger.e('Error getting device info: $e');
      return {
        'device_id': 'error_${DateTime.now().millisecondsSinceEpoch}',
        'device_name': 'Unknown Device',
        'model': 'Unknown',
        'brand': 'Unknown',
      };
    }
  }

  Future<void> signIn({
    required String nationalId,
    required String password,
  }) async {
    try {
      final email = '$<EMAIL>';
      _logger.i('Attempting to sign in with email: $email');
      
      // Sign in with email and password first
      final authResponse = await _supabaseClient.auth.signInWithPassword(
        email: email,
        password: password,
      );

      _logger.i('Auth response received: ${authResponse.user?.id}');

      if (authResponse.user == null) {
        throw const AuthException('Login failed', code: 'login_failed');
      }

      // التحقق من الجهاز باستخدام النظام المحسن
      final userId = authResponse.user?.id;
      _logger.i('User ID: $userId');

      if (userId == null) {
        throw const AuthException('User ID is null', code: 'user_id_null');
      }

      _logger.i('بدء التحقق من الجهاز باستخدام النظام المحسن للمستخدم: $userId');

      // التحقق من الجهاز أو ربطه إذا كان جديد
      final deviceAuthResult = await _enhancedDeviceAuth.verifyDevice(userId);

      if (!deviceAuthResult.success) {
        if (deviceAuthResult.isNewDevice) {
          // جهاز جديد - محاولة ربطه
          _logger.i('جهاز جديد - محاولة ربطه بالحساب');
          final bindResult = await _enhancedDeviceAuth.bindAccountToDevice(userId);

          if (!bindResult.success) {
            await signOut();
            throw AuthException(
              bindResult.message,
              code: _getAuthErrorCode(bindResult.trustLevel),
            );
          }

          _logger.i('تم ربط الجهاز الجديد بنجاح - مستوى الثقة: ${bindResult.trustLevel}');
        } else {
          // فشل التحقق من جهاز موجود
          await signOut();
          throw AuthException(
            deviceAuthResult.message,
            code: _getAuthErrorCode(deviceAuthResult.trustLevel),
          );
        }
      }

      _logger.i('تم التحقق من الجهاز بنجاح - مستوى الثقة: ${deviceAuthResult.trustLevel}');

      // حفظ معلومات الجهاز في قاعدة البيانات (اختياري)
      await _saveDeviceToDatabase(userId, deviceAuthResult);

      // حفظ بيانات المستخدم محلياً للعمل بدون إنترنت
      await _saveUserDataLocally(userId);

      _logger.i('Login successful for user: $userId');
      _logger.i('Current user session: ${_supabaseClient.auth.currentUser?.id}');
    } catch (e) {
      _logger.e('Login error: $e');

      if (e is AuthException) {
        rethrow;
      }

      final appError = ErrorHandler.handleError(e);
      throw appError;
    }
  }

  Future<void> signOut() async {
    try {
      // تسجيل الخروج من النظام المحلي
      final persistentAuth = PersistentAuthService();
      await persistentAuth.logout();

      await _supabaseClient.auth.signOut();
    } catch (e) {
      _logger.e('Error signing out: $e');
      final appError = ErrorHandler.handleError(e);
      throw appError;
    }
  }

  /// حفظ بيانات المستخدم محلياً
  Future<void> _saveUserDataLocally(String userId) async {
    try {
      final response = await _supabaseClient
          .from('users')
          .select('full_name')
          .eq('id', userId)
          .single();

      final persistentAuth = PersistentAuthService();
      await persistentAuth.saveUserData({
        'id': userId,
        'full_name': response['full_name'],
        'login_time': DateTime.now().toIso8601String(),
      });

      _logger.i('User data saved locally for offline access');
    } catch (e) {
      _logger.e('Error saving user data locally: $e');
      // لا نرمي خطأ هنا لأن تسجيل الدخول نجح
    }
  }

  Future<void> resetPassword(String email) async {
    try {
      await _supabaseClient.auth.resetPasswordForEmail(email);
    } catch (e) {
      final appError = ErrorHandler.handleError(e);
      throw appError;
    }
  }

  /// Get current user's devices
  Future<List<Map<String, dynamic>>> getCurrentUserDevices() async {
    try {
      final user = _supabaseClient.auth.currentUser;
      if (user == null) {
        throw const AuthException(
          'User not authenticated',
          code: 'user_not_authenticated',
        );
      }

      final response = await _supabaseClient
          .from('devices')
          .select()
          .eq('user_id', user.id)
          .order('created_at', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      _logger.e('Error getting user devices: $e');
      final appError = ErrorHandler.handleError(e);
      throw appError;
    }
  }

  /// تحويل مستوى الثقة إلى رمز خطأ
  String _getAuthErrorCode(DeviceTrustLevel trustLevel) {
    switch (trustLevel) {
      case DeviceTrustLevel.blocked:
        return 'device_blocked';
      case DeviceTrustLevel.suspicious:
        return 'device_suspicious';
      case DeviceTrustLevel.untrusted:
        return 'device_untrusted';
      default:
        return 'device_unauthorized';
    }
  }

  /// حفظ معلومات الجهاز في قاعدة البيانات باستخدام النظام الجديد
  Future<void> _saveDeviceToDatabase(String userId, DeviceAuthResult deviceResult) async {
    try {
      if (deviceResult.fingerprintData == null) return;

      final deviceData = deviceResult.fingerprintData!;

      // استخدام الدالة الجديدة verify_device_enhanced
      final result = await _supabaseClient.rpc('verify_device_enhanced', params: {
        'p_user_id': userId,
        'p_device_fingerprint': deviceResult.fingerprint,
        'p_android_id': deviceData.androidId,
        'p_build_fingerprint': deviceData.buildFingerprint,
        'p_confidence_score': deviceResult.confidenceScore,
        'p_trust_level': _trustLevelToString(deviceResult.trustLevel),
        'p_device_name': '${deviceData.manufacturer} ${deviceData.deviceModel}',
        'p_device_model': deviceData.deviceModel,
        'p_device_brand': deviceData.manufacturer,
        'p_device_product': deviceData.product,
        'p_device_hardware': deviceData.hardware,
        'p_hardware_info': deviceData.hardwareInfo,
        'p_system_info': deviceData.systemInfo,
        'p_screen_info': deviceData.screenInfo,
        'p_cpu_info': deviceData.cpuInfo,
        'p_storage_info': deviceData.storageInfo,
        'p_raw_data': deviceData.rawData,
      });

      if (result != null && result['status'] == 'success') {
        final action = result['action'];
        final deviceId = result['device_id'];

        if (action == 'created') {
          _logger.i('تم حفظ الجهاز الجديد في قاعدة البيانات - ID: $deviceId');
        } else if (action == 'updated') {
          _logger.i('تم تحديث معلومات الجهاز في قاعدة البيانات - ID: $deviceId');
        }
      } else if (result != null && result['status'] == 'error') {
        final errorCode = result['code'];
        final message = result['message'];

        if (errorCode == 'DEVICE_LIMIT_REACHED') {
          _logger.w('تم الوصول للحد الأقصى من الأجهزة: $message');
          // يمكن إضافة منطق إضافي هنا مثل عرض رسالة للمستخدم
        } else {
          _logger.e('خطأ في حفظ الجهاز: $message');
        }
      }

    } catch (e) {
      _logger.e('خطأ في حفظ معلومات الجهاز: $e');
      // لا نرمي خطأ هنا لأن المصادقة نجحت
    }
  }

  /// تحويل مستوى الثقة إلى نص
  String _trustLevelToString(DeviceTrustLevel trustLevel) {
    switch (trustLevel) {
      case DeviceTrustLevel.high:
        return 'high';
      case DeviceTrustLevel.medium:
        return 'medium';
      case DeviceTrustLevel.low:
        return 'low';
      case DeviceTrustLevel.untrusted:
        return 'untrusted';
      case DeviceTrustLevel.suspicious:
        return 'suspicious';
      case DeviceTrustLevel.blocked:
        return 'blocked';
    }
  }

  /// الحصول على قائمة أجهزة المستخدم
  Future<Map<String, dynamic>?> getUserDevices() async {
    try {
      final user = _supabaseClient.auth.currentUser;
      if (user == null) {
        _logger.w('لا يوجد مستخدم مسجل دخول');
        return null;
      }

      final result = await _supabaseClient.rpc('get_user_devices', params: {
        'p_user_id': user.id,
      });

      if (result != null && result['status'] == 'success') {
        _logger.i('تم الحصول على قائمة الأجهزة بنجاح - عدد الأجهزة: ${result['device_count']}');
        return result;
      } else {
        _logger.e('فشل في الحصول على قائمة الأجهزة');
        return null;
      }
    } catch (e) {
      _logger.e('خطأ في الحصول على قائمة الأجهزة: $e');
      return null;
    }
  }

  /// حذف جهاز
  Future<bool> removeDevice(String deviceId) async {
    try {
      final user = _supabaseClient.auth.currentUser;
      if (user == null) {
        _logger.w('لا يوجد مستخدم مسجل دخول');
        return false;
      }

      final result = await _supabaseClient.rpc('remove_device', params: {
        'p_user_id': user.id,
        'p_device_id': deviceId,
      });

      if (result != null && result['status'] == 'success') {
        _logger.i('تم حذف الجهاز بنجاح: ${result['message']}');
        return true;
      } else {
        final message = result?['message'] ?? 'فشل في حذف الجهاز';
        _logger.e('فشل في حذف الجهاز: $message');
        return false;
      }
    } catch (e) {
      _logger.e('خطأ في حذف الجهاز: $e');
      return false;
    }
  }

  /// تسجيل محاولة مصادقة فاشلة
  Future<void> recordFailedAuth(String deviceFingerprint, String androidId) async {
    try {
      final user = _supabaseClient.auth.currentUser;
      if (user == null) return;

      final result = await _supabaseClient.rpc('record_failed_auth', params: {
        'p_user_id': user.id,
        'p_device_fingerprint': deviceFingerprint,
        'p_android_id': androidId,
      });

      if (result != null) {
        final status = result['status'];
        final message = result['message'];

        if (status == 'blocked') {
          _logger.w('تم حجب الجهاز: $message');
        } else if (status == 'failed') {
          final attempts = result['attempts'];
          final remaining = result['remaining_attempts'];
          _logger.w('محاولة مصادقة فاشلة - المحاولات: $attempts، المتبقي: $remaining');
        }
      }
    } catch (e) {
      _logger.e('خطأ في تسجيل المحاولة الفاشلة: $e');
    }
  }
}
