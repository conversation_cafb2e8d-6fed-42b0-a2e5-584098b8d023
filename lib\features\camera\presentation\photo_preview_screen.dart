import 'dart:io';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:share_plus/share_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/services/photos_service.dart';
import '../../../core/utils/image_processor.dart';
import '../../../core/services/supabase_service.dart';
import '../../../core/services/auto_upload_service.dart';
import 'package:logger/logger.dart';

class PhotoPreviewScreen extends ConsumerStatefulWidget {
  final String photoPath;
  final String? location;

  const PhotoPreviewScreen({
    super.key,
    required this.photoPath,
    this.location,
  });

  @override
  ConsumerState<PhotoPreviewScreen> createState() => _PhotoPreviewScreenState();
}

class _PhotoPreviewScreenState extends ConsumerState<PhotoPreviewScreen> {
  String? _processedImagePath;
  bool _isUploading = false;
  bool _isUploaded = false;
  bool _isProcessing = true;
  final _logger = Logger();

  @override
  void initState() {
    super.initState();
    // بدء معالجة الصورة في الخلفية
    _processImage();
  }

  Future<void> _processImage() async {
    try {
      final user = ref.read(supabaseServiceProvider).client.auth.currentUser;
      String username = '';

      if (user != null) {
        final response = await ref.read(supabaseServiceProvider).client
            .from('users')
            .select('full_name')
            .eq('id', user.id)
            .single();
        username = response['full_name'] as String? ?? '';
      }

      final processedPath = await ImageProcessor.processImage(
        imagePath: widget.photoPath,
        username: username,
        location: widget.location ?? 'لم يتم تحديد الموقع',
      );

      if (mounted) {
        setState(() {
          _processedImagePath = processedPath;
          _isProcessing = false;
        });
      }
    } catch (e) {
      _logger.e('Error processing image: $e');
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  Future<void> _saveAndShare() async {
    if (_processedImagePath == null) return;

    if (!_isUploaded) {
      setState(() {
        _isUploading = true;
      });

      try {
        // حفظ الصورة في المعرض المحلي دائماً
        await _saveToGallery();

        // بدء الرفع التلقائي في الخلفية
        _startBackgroundUpload();

        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('camera.saved_to_gallery'.tr()),
            backgroundColor: Colors.green,
          ),
        );

        setState(() {
          _isUploaded = true;
          _isUploading = false;
        });
      } catch (e) {
        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('camera.save_error'.tr()),
            backgroundColor: Colors.red,
          ),
        );

        setState(() {
          _isUploading = false;
        });
        return;
      }
    } else {
      // مشاركة الصورة
      try {
        await Share.shareXFiles([XFile(_processedImagePath!)]);
      } catch (e) {
        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('camera.share_error'.tr()),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _saveToGallery() async {
    // إنشاء مجلد المعرض المحلي
    final directory = await getApplicationDocumentsDirectory();
    final galleryDir = Directory('${directory.path}/gallery');
    if (!await galleryDir.exists()) {
      await galleryDir.create(recursive: true);
    }

    // نسخ الصورة إلى المعرض
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final fileName = 'photo_$timestamp.jpg';
    final savedPath = '${galleryDir.path}/$fileName';

    await File(_processedImagePath!).copy(savedPath);

    // حفظ معلومات الصورة للرفع التلقائي
    await _saveUploadMetadata(savedPath, fileName);

    _logger.i('Photo saved to gallery: $savedPath');
  }

  Future<void> _saveUploadMetadata(String filePath, String fileName) async {
    final prefs = await SharedPreferences.getInstance();
    final pendingUploads = prefs.getStringList('pending_uploads') ?? [];

    final metadata = {
      'file_path': filePath,
      'file_name': fileName,
      'location': widget.location ?? 'unknown',
      'created_at': DateTime.now().toIso8601String(),
      'type': 'photo',
      'status': 'pending', // pending, uploading, uploaded, failed
    };

    pendingUploads.add(jsonEncode(metadata));
    await prefs.setStringList('pending_uploads', pendingUploads);
  }

  void _startBackgroundUpload() {
    // بدء الرفع التلقائي في الخلفية
    AutoUploadService().startAutoUpload();
    _logger.i('Background upload started');
  }

  void _retakePhoto() {
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // عرض الصورة (الأصلية أولاً ثم المعالجة)
          Center(
            child: Image.file(
              File(_processedImagePath ?? widget.photoPath),
              fit: BoxFit.contain,
            ),
          ),

          // مؤشر المعالجة (يظهر فقط أثناء المعالجة)
          if (_isProcessing)
            Positioned(
              top: 50,
              right: 20,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'camera.processing'.tr(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          
          // شريط الأزرار
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  colors: [
                    Colors.black.withAlpha((0.8 * 255).round()),
                    Colors.transparent,
                  ],
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // زر إعادة التصوير
                  IconButton(
                    icon: const Icon(
                      Icons.refresh_rounded,
                      color: Colors.white,
                      size: 32,
                    ),
                    onPressed: _retakePhoto,
                  ),
                  
                  // زر الرفع/المشاركة
                  _isUploading
                      ? const CircularProgressIndicator(color: Colors.white)
                      : IconButton(
                          icon: Icon(
                            _isUploaded ? Icons.share_rounded : Icons.check_rounded,
                            color: Colors.white,
                            size: 32,
                          ),
                          onPressed: _saveAndShare,
                        ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
