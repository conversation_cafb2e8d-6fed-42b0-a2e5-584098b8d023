import 'dart:async';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../../core/services/supabase_service.dart';
import '../../../core/services/permissions_service.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/logger.dart';
import './photo_preview_screen.dart';
import './video_preview_screen.dart';
import './gallery_screen.dart';
import './widgets/location_time_overlay.dart';
import 'package:easy_localization/easy_localization.dart';

class CameraScreen extends ConsumerStatefulWidget {
  final String? selectedLocation;

  const CameraScreen({super.key, this.selectedLocation});

  @override
  ConsumerState<CameraScreen> createState() => _CameraScreenState();
}

class _CameraScreenState extends ConsumerState<CameraScreen> {
  final _logger = getLogger();
  CameraController? _controller;
  bool _isInitialized = false;
  bool _isProcessing = false;
  bool _isRecording = false;
  Timer? _videoTimer;
  Timer? _locationTimer;
  Position? _currentPosition;
  String? _currentAddress;
  String? _username;
  bool _isNightMode = false;
  bool _isDayMode = false;
  Offset? _focusPoint;

  double _baseScale = 1.0;
  double _currentScale = 1.0;
  double _minAvailableZoom = 1.0;
  double _maxAvailableZoom = 1.0;
  double _minAvailableExposure = 0.0;
  double _maxAvailableExposure = 0.0;
  double _currentExposure = 0.0;

  // متغيرات تصوير الفيديو
  bool _isRecordingVideo = false;
  bool _isVideoMode = false;

  @override
  void initState() {
    super.initState();
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    _checkPermissionsAndInitialize();
  }

  Future<void> _checkPermissionsAndInitialize() async {
    try {
      final permissionsService = ref.read(permissionsServiceProvider);

      // فحص الأذونات أولاً
      bool hasPermissions = await permissionsService.checkAllPermissions();

      if (!hasPermissions) {
        // طلب الأذونات
        hasPermissions = await permissionsService.requestAllPermissions();
      }

      if (hasPermissions) {
        // إذا تم منح الأذونات، ابدأ تهيئة الكاميرا
        await _initializeCamera();
        await _initializeLocation();
        _startLocationUpdates();
        await _loadUsername();
      } else {
        // إذا لم يتم منح الأذونات، اعرض رسالة خطأ
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('permissions.camera_required'.tr()),
              backgroundColor: Colors.red,
              action: SnackBarAction(
                label: 'common.settings'.tr(),
                textColor: Colors.white,
                onPressed: () => openAppSettings(),
              ),
            ),
          );
        }
      }
    } catch (e) {
      _logger.e('Error checking permissions: $e');
    }
  }

  void _startLocationUpdates() {
    // تحديث الموقع كل 5 دقائق بدلاً من كل دقيقة لتوفير البطارية
    _locationTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      _initializeLocation();
    });
  }

  @override
  void dispose() {
    // إلغاء جميع العمليات الجارية
    _videoTimer?.cancel();
    _locationTimer?.cancel();

    // إعادة تعيين اتجاه الشاشة
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);

    // تحرير موارد الكاميرا
    _controller?.dispose();
    _controller = null;

    super.dispose();
  }

  Future<void> _initializeLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        _logger.e('Location services are disabled');
        setState(() {
          _currentAddress = 'الرجاء تفعيل خدمة الموقع';
        });
        return;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          _logger.e('Location permissions denied');
          setState(() {
            _currentAddress = 'تم رفض إذن الوصول للموقع';
          });
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        _logger.e('Location permissions permanently denied');
        setState(() {
          _currentAddress = 'تم رفض إذن الوصول للموقع بشكل دائم';
        });
        return;
      }

      setState(() {
        _currentAddress = 'جاري تحديد الموقع...';
      });

      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          distanceFilter: 10,
        ),
      );

      final placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );

      if (placemarks.isNotEmpty) {
        final place = placemarks.first;

        final List<String> addressParts = [];

        final String line1 = [
          place.country ?? '',
          place.administrativeArea ?? '',
        ].where((part) => part.isNotEmpty).join(' ');

        if (line1.isNotEmpty) {
          addressParts.add(line1);
        }

        final String line2 = [
          place.subAdministrativeArea ?? '',
          place.locality ?? '',
          place.subLocality ?? '',
        ].where((part) => part.isNotEmpty).join(' ');

        if (line2.isNotEmpty) {
          addressParts.add(line2);
        }

        if (place.thoroughfare?.isNotEmpty == true) {
          addressParts.add(place.thoroughfare!);
        }

        if (addressParts.isEmpty) {
          _logger.w('No address parts found');
          setState(() {
            _currentAddress = 'لم يتم العثور على عنوان';
          });
          return;
        }

        setState(() {
          _currentPosition = position;
          _currentAddress = addressParts.join('\n');
        });

        _logger.i('Location found: $_currentAddress');
      } else {
        _logger.w('No placemarks found');
        setState(() {
          _currentAddress = 'لم يتم العثور على عنوان';
        });
      }
    } catch (e) {
      _logger.e('Error getting location', error: e);
      setState(() {
        _currentAddress = 'حدث خطأ في تحديد الموقع';
      });
    }
  }

  Future<void> _initializeCamera() async {
    try {
      final cameras = await availableCameras();
      if (cameras.isEmpty) {
        _logger.w('No cameras available');
        return;
      }

      final camera = cameras.first;
      _controller = CameraController(
        camera,
        ResolutionPreset.ultraHigh, // أعلى دقة متاحة
        enableAudio: false,
        imageFormatGroup: ImageFormatGroup.jpeg,
      );

      await _controller!.initialize();

      // تحديث إعدادات الكاميرا بعد التهيئة مع تحسين الأداء
      await Future.wait([
        _controller!.setFlashMode(FlashMode.off),
        _controller!.setFocusMode(FocusMode.auto),
        _controller!.setExposureMode(ExposureMode.auto),
      ]);

      // تهيئة إعدادات التكبير
      _minAvailableZoom = await _controller!.getMinZoomLevel();
      _maxAvailableZoom = await _controller!.getMaxZoomLevel();
      _currentScale = _baseScale;

      // تهيئة إعدادات التعريض
      _minAvailableExposure = await _controller!.getMinExposureOffset();
      _maxAvailableExposure = await _controller!.getMaxExposureOffset();
      _currentExposure = 0.0;

      if (mounted) {
        setState(() {
          _isInitialized = true;
        });
      }
    } catch (e) {
      _logger.e('Error initializing camera', error: e);

      // في حالة فشل التهيئة، حاول مرة أخرى بدقة أقل
      try {
        if (_controller != null) {
          await _controller!.dispose();
        }

        final cameras = await availableCameras();
        if (cameras.isNotEmpty) {
          final camera = cameras.first;
          _controller = CameraController(
            camera,
            ResolutionPreset.medium, // دقة أقل كبديل
            enableAudio: false,
            imageFormatGroup: ImageFormatGroup.jpeg,
          );

          await _controller!.initialize();

          if (mounted) {
            setState(() {
              _isInitialized = _controller!.value.isInitialized;
            });
          }

          _logger.i('Camera initialized with medium resolution as fallback');
        }
      } catch (fallbackError) {
        _logger.e('Failed to initialize camera even with fallback', error: fallbackError);
      }
    }
  }

  Future<void> _loadUsername() async {
    try {
      final user = ref.read(supabaseServiceProvider).client.auth.currentUser;
      _logger.i('Loading username for user: ${user?.id}');

      if (user != null) {
        final response = await ref.read(supabaseServiceProvider).client
            .from('users')
            .select('full_name')
            .eq('id', user.id)
            .single();

        if (mounted) {
          setState(() {
            _username = response['full_name'] as String?;
            _logger.i('Username loaded: $_username');
          });
        }
      } else {
        _logger.w('No user logged in');
      }
    } catch (e) {
      _logger.e('Error loading username', error: e);
    }
  }

  Future<void> _takePhoto() async {
    if (_controller == null || !_isInitialized || _isProcessing) return;

    setState(() => _isProcessing = true);

    // إضافة تأخير قصير لضمان استقرار الكاميرا
    await Future.delayed(const Duration(milliseconds: 100));

    try {
      if (_isNightMode) {
        // تحسين التقاط الصور الليلية
        await Future.wait([
          _controller!.setFocusMode(FocusMode.auto),
          _controller!.setExposureMode(ExposureMode.auto),
        ]);

        // انتظار لتحسين التركيز
        await Future.delayed(const Duration(milliseconds: 500));

        // التقاط صورة واحدة محسنة
        final xFile = await _controller!.takePicture();
        
        if (!mounted) return;
        
        final result = await Navigator.push<bool>(
          context,
          MaterialPageRoute(
            builder: (context) => PhotoPreviewScreen(
              photoPath: xFile.path,
              location: _currentAddress,
            ),
          ),
        );

        if (result == false) return;
      } else {
        // التقاط الصور النهارية بشكل عادي
        await _controller!.setFocusMode(FocusMode.auto);
        await Future.delayed(const Duration(milliseconds: 300));
        
        final xFile = await _controller!.takePicture();
        
        if (!mounted) return;
        
        final result = await Navigator.push<bool>(
          context,
          MaterialPageRoute(
            builder: (context) => PhotoPreviewScreen(
              photoPath: xFile.path,
              location: _currentAddress,
            ),
          ),
        );

        if (result == false) return;
      }
    } catch (e) {
      _logger.e('Error taking photo', error: e);
      if (!mounted) return;
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('camera.photo_error'.tr()),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() => _isProcessing = false);
      }
    }
  }

  Future<void> _startVideoRecording() async {
    if (_controller == null || !_isInitialized || _isProcessing || _isRecording) return;

    try {
      await _controller!.startVideoRecording();
      setState(() {
        _isRecording = true;
        _isRecordingVideo = true;
      });
    } catch (e) {
      _logger.e('Error starting video recording', error: e);
    }
  }

  Future<void> _stopVideoRecording() async {
    if (_controller == null || !_isInitialized || !_isRecording) return;

    try {
      final video = await _controller!.stopVideoRecording();
      setState(() {
        _isRecording = false;
        _isRecordingVideo = false;
      });
      _logger.i('Video recorded: ${video.path}');

      // عرض شاشة معاينة الفيديو
      if (!mounted) return;
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => VideoPreviewScreen(
            videoPath: video.path,
            selectedLocation: widget.selectedLocation,
            currentAddress: _currentAddress, // تمرير الموقع الجغرافي الحقيقي
          ),
        ),
      );
    } catch (e) {
      _logger.e('Error stopping video recording', error: e);
    }
  }



  Future<void> _toggleNightMode() async {
    if (_isDayMode) {
      setState(() => _isDayMode = false);
    }
    
    setState(() => _isNightMode = !_isNightMode);
    
    if (_controller == null) return;

    try {
      if (_isNightMode) {
        // تفعيل الوضع الليلي
        await _controller!.setExposureMode(ExposureMode.auto);
        await _controller!.setExposureOffset(0.7);
        await _controller!.setFlashMode(FlashMode.off);
        await _controller!.setFocusMode(FocusMode.auto);
      } else {
        // العودة للوضع العادي
        await _controller!.setExposureMode(ExposureMode.auto);
        await _controller!.setExposureOffset(0.0);
        await _controller!.setFlashMode(FlashMode.off);
        await _controller!.setFocusMode(FocusMode.auto);
      }
    } catch (e) {
      _logger.e('Error toggling night mode', error: e);
    }
  }

  Future<void> _enableDayMode() async {
    if (_isNightMode) {
      setState(() => _isNightMode = false);
    }
    
    setState(() => _isDayMode = !_isDayMode);
    
    if (_controller == null) return;

    try {
      if (_isDayMode) {
        // تفعيل الوضع النهاري
        await _controller!.setExposureMode(ExposureMode.auto);
        await _controller!.setExposureOffset(-0.5);
        await _controller!.setFlashMode(FlashMode.off);
        await _controller!.setFocusMode(FocusMode.auto);
      } else {
        // العودة للوضع العادي
        await _controller!.setExposureMode(ExposureMode.auto);
        await _controller!.setExposureOffset(0.0);
        await _controller!.setFlashMode(FlashMode.off);
        await _controller!.setFocusMode(FocusMode.auto);
      }
    } catch (e) {
      _logger.e('Error toggling day mode', error: e);
    }
  }

  Future<void> _onTapToFocus(TapDownDetails details, BoxConstraints constraints) async {
    if (_controller == null || !_isInitialized) return;

    final offset = Offset(
      details.localPosition.dx / constraints.maxWidth,
      details.localPosition.dy / constraints.maxHeight,
    );

    try {
      await Future.wait([
        _controller!.setFocusPoint(offset),
        _controller!.setExposurePoint(offset),
      ]);

      setState(() {
        _focusPoint = details.localPosition;
      });

      await Future.delayed(const Duration(seconds: 2));
      if (mounted) {
        setState(() {
          _focusPoint = null;
        });
      }
    } catch (e) {
      _logger.w('Could not focus on point', error: e);
    }
  }

  // تبديل وضع التصوير (صورة/فيديو)
  void _toggleCameraMode() {
    setState(() {
      _isVideoMode = !_isVideoMode;
    });
  }

  // فتح معرض الصور والفيديوهات
  void _openGallery() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const GalleryScreen(),
      ),
    );
  }

  // بناء أزرار التحكم
  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.6),
          borderRadius: BorderRadius.circular(25),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 24,
        ),
      ),
    );
  }

  // بناء أزرار وضع التصوير
  Widget _buildModeButton({
    required String text,
    required IconData icon,
    required bool isActive,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isActive
              ? const Color(0xFFD4AF37)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isActive ? Colors.black : Colors.white,
              size: 16,
            ),
            const SizedBox(width: 6),
            Text(
              text,
              style: TextStyle(
                color: isActive ? Colors.black : Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return Scaffold(
        backgroundColor: Colors.black,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CircularProgressIndicator(color: Colors.white),
              const SizedBox(height: 20),
              Text(
                'camera.initializing'.tr(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      );
    }

    final isArabic = Localizations.localeOf(context).languageCode == 'ar';
    final appTitle = isArabic ? 'ذاكرة القمر' : 'Moon Memory';

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        toolbarHeight: 48,
        centerTitle: true,
        title: Text(
          appTitle,
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
            fontFamily: isArabic ? 'Cairo' : null,
          ),
        ),
        actions: const [],
      ),
      backgroundColor: Colors.black,
      body: Column(
        children: [
          Expanded(
            child: Stack(
              fit: StackFit.expand,
              children: [
                GestureDetector(
                  onTapDown: (details) => _onTapToFocus(details, BoxConstraints.tight(MediaQuery.of(context).size)),
                  onScaleStart: (details) {
                    _baseScale = _currentScale;
                  },
                  onScaleUpdate: (details) {
                    if (_controller == null) return;

                    _currentScale = (_baseScale * details.scale)
                        .clamp(_minAvailableZoom, _maxAvailableZoom);

                    _controller!.setZoomLevel(_currentScale);
                    setState(() {});
                  },
                  child: Stack(
                    fit: StackFit.expand,
                    children: [
                      CameraPreview(_controller!),

                      // مؤشر التكبير/التصغير
                      if (_currentScale != 1.0)
                        Positioned(
                          top: 100,
                          left: 20,
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              color: Colors.black.withValues(alpha: 0.7),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Text(
                              '${_currentScale.toStringAsFixed(1)}x',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),



                      // مؤشر التسجيل
                      if (_isRecording)
                        Positioned(
                          top: 180,
                          left: 20,
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              color: Colors.red.withValues(alpha: 0.8),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: const Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.fiber_manual_record,
                                  color: Colors.white,
                                  size: 12,
                                ),
                                SizedBox(width: 4),
                                Text(
                                  'تسجيل',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                      if (_focusPoint != null)
                        Positioned(
                          left: _focusPoint!.dx - 20,
                          top: _focusPoint!.dy - 20,
                          child: Container(
                            height: 40,
                            width: 40,
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: Colors.white,
                                width: 2,
                              ),
                              shape: BoxShape.circle,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
                Positioned(
                  top: -16,
                  right: 16,
                  child: Image.asset(
                    'assets/images/icon_right.png',
                    width: 130,
                    height: 130,
                  ),
                ),
                Positioned(
                  top: 8,
                  left: 16,
                  child: Image.asset(
                    'assets/images/icon_left.png',
                    width: 95,
                    height: 95,
                  ),
                ),
                Positioned(
                  top: 240,
                  right: 16,
                  child: RotatedBox(
                    quarterTurns: 3,
                    child: SizedBox(
                      width: 200,
                      child: Slider(
                        value: _currentExposure,
                        min: _minAvailableExposure,
                        max: _maxAvailableExposure,
                        activeColor: Colors.white,
                        inactiveColor: Colors.white30,
                        onChanged: (value) async {
                          setState(() => _currentExposure = value);
                          await _controller?.setExposureOffset(value);
                        },
                      ),
                    ),
                  ),
                ),
                Positioned(
                  bottom: 16,
                  right: 16,
                  child: LocationTimeOverlay(
                    currentPosition: _currentPosition,
                    address: _currentAddress,
                  ),
                ),
                if (_username != null)
                  Positioned(
                    bottom: 16,
                    left: 16,
                    child: Text(
                      _username!,
                      style: AppTheme.arabicText.copyWith(color: Colors.white),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        height: 120,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.black.withValues(alpha: 0.0),
              Colors.black.withValues(alpha: 0.8),
              Colors.black,
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // زر المعرض
                _buildControlButton(
                  icon: Icons.photo_library_rounded,
                  onTap: _openGallery,
                ),

                // أزرار وضع التصوير
                Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.6),
                    borderRadius: BorderRadius.circular(25),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      _buildModeButton(
                        text: 'صورة',
                        icon: Icons.camera_alt_rounded,
                        isActive: !_isVideoMode,
                        onTap: () {
                          setState(() {
                            _isVideoMode = false;
                          });
                        },
                      ),
                      const SizedBox(width: 8),
                      _buildModeButton(
                        text: 'فيديو',
                        icon: Icons.videocam_rounded,
                        isActive: _isVideoMode,
                        onTap: () {
                          setState(() {
                            _isVideoMode = true;
                          });
                        },
                      ),
                    ],
                  ),
                ),

                // زر التصوير الرئيسي
                GestureDetector(
                  onTapDown: (_) async {
                    if (_isVideoMode && !_isProcessing && !_isRecording) {
                      _videoTimer = Timer(const Duration(milliseconds: 300), () {
                        if (!_isProcessing) {
                          _startVideoRecording();
                        }
                      });
                    }
                  },
                  onTapUp: (_) async {
                    _videoTimer?.cancel();
                    if (_isRecording) {
                      await _stopVideoRecording();
                    } else if (!_isProcessing) {
                      if (_isVideoMode) {
                        // في وضع الفيديو، ابدأ التسجيل فوراً
                        await _startVideoRecording();
                      } else {
                        // في وضع الصورة، التقط صورة
                        await _takePhoto();
                      }
                    }
                  },
                  onTapCancel: () {
                    _videoTimer?.cancel();
                  },
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: _isRecording
                          ? const LinearGradient(
                              colors: [Color(0xFFFF4444), Color(0xFFCC0000)],
                            )
                          : _isVideoMode
                              ? const LinearGradient(
                                  colors: [Color(0xFFFF6B6B), Color(0xFFEE5A24)],
                                )
                              : const LinearGradient(
                                  colors: [Colors.white, Color(0xFFF0F0F0)],
                                ),
                      boxShadow: [
                        BoxShadow(
                          color: _isRecording
                              ? Colors.red.withValues(alpha: 0.5)
                              : Colors.black.withValues(alpha: 0.3),
                          blurRadius: 10,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                    child: Center(
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 200),
                        width: _isRecording ? 30 : 60,
                        height: _isRecording ? 30 : 60,
                        decoration: BoxDecoration(
                          color: _isRecording
                              ? Colors.white
                              : _isVideoMode
                                  ? Colors.white
                                  : const Color(0xFFD4AF37),
                          borderRadius: BorderRadius.circular(_isRecording ? 6 : 30),
                        ),
                        child: _isRecording
                            ? null
                            : Icon(
                                _isVideoMode ? Icons.videocam : Icons.camera_alt,
                                color: _isVideoMode ? const Color(0xFFFF4444) : Colors.white,
                                size: 24,
                              ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
