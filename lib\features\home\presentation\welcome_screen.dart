import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../core/services/supabase_service.dart';
import '../../../core/services/persistent_auth_service.dart';
import '../../../core/services/permissions_service.dart';
import '../../../core/services/auth_service.dart';
import '../../../core/routing/app_router.dart';
import '../../../core/utils/logger.dart';
import '../../camera/presentation/camera_screen.dart';

class WelcomeScreen extends ConsumerStatefulWidget {
  const WelcomeScreen({super.key});

  @override
  ConsumerState<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends ConsumerState<WelcomeScreen>
    with TickerProviderStateMixin {
  final _logger = getLogger();
  String? _username;
  bool _isLoading = true;
  String _selectedLocation = 'main_office'; // الموقع الافتراضي
  
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // قائمة المواقع المتاحة
  final List<Map<String, String>> _locations = [
    {'id': 'main_office', 'name_ar': 'المكتب الرئيسي', 'name_en': 'Main Office'},
    {'id': 'branch_1', 'name_ar': 'الفرع الأول', 'name_en': 'Branch 1'},
    {'id': 'branch_2', 'name_ar': 'الفرع الثاني', 'name_en': 'Branch 2'},
    {'id': 'field_work', 'name_ar': 'العمل الميداني', 'name_en': 'Field Work'},
    {'id': 'meeting_room', 'name_ar': 'قاعة الاجتماعات', 'name_en': 'Meeting Room'},
  ];

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _loadUserData();
  }

  void _initAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _fadeController.forward();
    _slideController.forward();
  }

  Future<void> _loadUserData() async {
    try {
      final persistentAuth = ref.read(persistentAuthServiceProvider);
      final userData = await persistentAuth.getUserData();
      
      if (userData != null) {
        setState(() {
          _username = userData['full_name'] ?? 'المستخدم';
          _isLoading = false;
        });
      } else {
        // محاولة الحصول على البيانات من Supabase
        final user = ref.read(supabaseServiceProvider).client.auth.currentUser;
        if (user != null) {
          final response = await ref.read(supabaseServiceProvider).client
              .from('users')
              .select('full_name')
              .eq('id', user.id)
              .single();
          
          final fullName = response['full_name'] as String?;
          setState(() {
            _username = fullName ?? 'المستخدم';
            _isLoading = false;
          });
          
          // حفظ البيانات محلياً
          await persistentAuth.saveUserData({
            'id': user.id,
            'full_name': fullName,
          });
        }
      }
    } catch (e) {
      _logger.e('Error loading user data: $e');
      setState(() {
        _username = 'المستخدم';
        _isLoading = false;
      });
    }
  }

  Future<void> _openCamera() async {
    try {
      // فحص الأذونات أولاً
      final permissionsService = ref.read(permissionsServiceProvider);
      bool hasPermissions = await permissionsService.checkAllPermissions();

      if (!hasPermissions) {
        hasPermissions = await permissionsService.requestAllPermissions();
      }

      if (!mounted) return;

      if (hasPermissions) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => CameraScreen(selectedLocation: _selectedLocation),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('permissions.camera_required'.tr()),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      _logger.e('Error opening camera: $e');
    }
  }

  void _changeLanguage() {
    final currentLocale = context.locale;
    final newLocale = currentLocale.languageCode == 'ar' 
        ? const Locale('en') 
        : const Locale('ar');
    
    context.setLocale(newLocale);
  }

  String _getLocationName(Map<String, String> location) {
    final isArabic = context.locale.languageCode == 'ar';
    return isArabic ? location['name_ar']! : location['name_en']!;
  }

  Future<void> _handleSignOut() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('auth.logout'.tr()),
        content: Text('auth.logout_confirm'.tr()),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(
              'common.cancel'.tr(),
              style: const TextStyle(color: Colors.grey),
            ),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(
              'auth.logout'.tr(),
              style: const TextStyle(color: Color(0xFFD4AF37)),
            ),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      final authService = ref.read(authServiceProvider);
      await authService.signOut();

      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('auth.logout_success'.tr()),
          backgroundColor: Colors.green,
        ),
      );

      if (!mounted) return;

      Navigator.of(context).pushNamedAndRemoveUntil(
        AppRoutes.login,
        (route) => false,
      );
    } catch (e) {
      _logger.e('Error signing out: $e');
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('errors.general'.tr()),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF2C1810), // بني داكن دافئ
              Color(0xFF3D2817), // بني متوسط
              Color(0xFF5D4037), // بني شوكولاتة
              Color(0xFF8D6E63), // بني فاتح
              Color(0xFFBCAAA4), // بيج دافئ
            ],
            stops: [0.0, 0.25, 0.5, 0.75, 1.0],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  children: [
                    // شريط علوي مع أزرار اللغة وتسجيل الخروج
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'app.name'.tr(),
                          style: GoogleFonts.cairo(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFFD4AF37),
                          ),
                        ),
                        Row(
                          children: [
                            IconButton(
                              onPressed: _changeLanguage,
                              icon: const Icon(
                                Icons.language,
                                color: Colors.white,
                                size: 28,
                              ),
                            ),
                            IconButton(
                              onPressed: _handleSignOut,
                              icon: const Icon(
                                Icons.logout_rounded,
                                color: Colors.white,
                                size: 24,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 40),
                    
                    // رسالة الترحيب
                    if (_isLoading)
                      const CircularProgressIndicator(color: Colors.white)
                    else
                      Column(
                        children: [
                          Text(
                            'welcome.greeting'.tr(),
                            style: GoogleFonts.cairo(
                              fontSize: 24,
                              fontWeight: FontWeight.w300,
                              color: Colors.white.withValues(alpha: 0.9),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            _username ?? 'المستخدم',
                            style: GoogleFonts.cairo(
                              fontSize: 32,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    
                    const SizedBox(height: 60),
                    
                    // لوحة تحديد الموقع
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.15),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'welcome.select_location'.tr(),
                            style: GoogleFonts.cairo(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 16),
                          DropdownButtonFormField<String>(
                            value: _selectedLocation,
                            decoration: InputDecoration(
                              filled: true,
                              fillColor: Colors.white,
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide.none,
                              ),
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                            ),
                            items: _locations.map((location) {
                              return DropdownMenuItem<String>(
                                value: location['id'],
                                child: Text(
                                  _getLocationName(location),
                                  style: GoogleFonts.cairo(
                                    fontSize: 14,
                                    color: Colors.black87,
                                  ),
                                ),
                              );
                            }).toList(),
                            onChanged: (value) {
                              if (value != null) {
                                setState(() {
                                  _selectedLocation = value;
                                });
                              }
                            },
                          ),
                        ],
                      ),
                    ),
                    
                    const Spacer(),
                    
                    // زر فتح الكاميرا
                    Container(
                      width: double.infinity,
                      height: 120,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(30),
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.4),
                          width: 2,
                        ),
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: _openCamera,
                          borderRadius: BorderRadius.circular(30),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(
                                Icons.camera_alt_rounded,
                                size: 48,
                                color: Colors.white,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'welcome.open_camera'.tr(),
                                style: GoogleFonts.cairo(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.white,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 40),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
